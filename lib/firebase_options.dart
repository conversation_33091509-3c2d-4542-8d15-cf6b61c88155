// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCuDEpgaRHwfL9DgR53SnT4c1Hr03jmrnA',
    appId: '1:457377941870:web:d3c40c6cee92e4f443997f',
    messagingSenderId: '457377941870',
    projectId: 'twin-market-app',
    authDomain: 'twin-market-app.firebaseapp.com',
    storageBucket: 'twin-market-app.firebasestorage.app',
    measurementId: 'G-FZ7PM9Q7P2',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBesLKto7LAxfAzn1jFkQiZdhS5op5UJUw',
    appId: '1:457377941870:android:7434f862f4cb098b43997f',
    messagingSenderId: '457377941870',
    projectId: 'twin-market-app',
    storageBucket: 'twin-market-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA6R3m_Phbcy3oTfg6GXa19btoto5lycIo',
    appId: '1:457377941870:ios:8ebfee4f73388bba43997f',
    messagingSenderId: '457377941870',
    projectId: 'twin-market-app',
    storageBucket: 'twin-market-app.firebasestorage.app',
    iosBundleId: 'com.ajory.twinDeliveryApp',
  );
}
