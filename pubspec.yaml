name: sixam_mart_delivery
description: A new Flutter application.
publish_to: 'none' #
version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  get: ^4.6.6
  shared_preferences: ^2.3.2
  connectivity_plus: ^6.0.5
  firebase_core: ^3.4.1
  firebase_messaging: ^15.1.1
  firebase_auth: ^5.2.1
  flutter_local_notifications: ^17.2.2
  path_provider: ^2.1.4
  url_strategy: ^0.3.0
  pin_code_fields: ^8.0.1
  http: ^1.2.2
  flutter_switch: ^0.3.2
  shimmer_animation: ^2.2.1
  geolocator: ^13.0.1
  geocoding: ^3.0.0
  google_maps_flutter: ^2.9.0
  url_launcher: ^6.3.0
  vibration: ^2.0.0
  image_picker: ^1.1.2
  phone_numbers_parser: ^9.0.0
  country_code_picker: ^3.0.0
  audioplayers: ^6.1.0
  flutter_widget_from_html_core: ^0.15.2
  cached_network_image: ^3.4.1
  path: ^1.9.0
  http_parser: ^4.1.0
  intl: ^0.19.0
  dotted_border: ^2.1.0
  photo_view: ^0.15.0
  web_socket_channel: ^3.0.1
  flutter_inappwebview: ^6.0.0
  just_the_tooltip: ^0.0.12
  flutter_svg: ^2.0.10+1
  permission_handler: ^11.4.0
  flutter_foreground_task: ^8.17.0
  dropdown_button2: ^2.3.9
  open_filex: ^4.7.0

dependency_overrides:
  collection: ^1.19.0
  web: ^1.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/image/
    - assets/language/
    - assets/notification.mp3

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/font/Roboto-Regular.ttf
          weight: 400
        - asset: assets/font/Roboto-Medium.ttf
          weight: 500
        - asset: assets/font/Roboto-Bold.ttf
          weight: 700
        - asset: assets/font/Roboto-Black.ttf
          weight: 900