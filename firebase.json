{"flutter": {"platforms": {"android": {"default": {"projectId": "twin-market-app", "appId": "1:457377941870:android:7434f862f4cb098b43997f", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "twin-market-app", "appId": "1:457377941870:ios:8ebfee4f73388bba43997f", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "twin-market-app", "configurations": {"android": "1:457377941870:android:7434f862f4cb098b43997f", "ios": "1:457377941870:ios:8ebfee4f73388bba43997f", "web": "1:457377941870:web:d3c40c6cee92e4f443997f"}}}}}}