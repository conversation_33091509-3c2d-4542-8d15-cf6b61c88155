                        -H/Users/<USER>/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-D<PERSON>DROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Projects/6amMart-Delivery-App/build/app/intermediates/cxx/RelWithDebInfo/58x1b463/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Projects/6amMart-Delivery-App/build/app/intermediates/cxx/RelWithDebInfo/58x1b463/obj/x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/Projects/6amMart-Delivery-App/android/app/.cxx/RelWithDebInfo/58x1b463/x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2