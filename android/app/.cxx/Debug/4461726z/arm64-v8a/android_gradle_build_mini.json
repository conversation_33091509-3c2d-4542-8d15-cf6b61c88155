{"buildFiles": ["/Users/<USER>/Documents/FlutterSdk/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/6amtech/6ammart/6amMart-Delivery-App/android/app/.cxx/Debug/4461726z/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/6amtech/6ammart/6amMart-Delivery-App/android/app/.cxx/Debug/4461726z/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}